"use client"

import { createContext, useContext, ReactNode } from 'react'

interface AuthContextType {
  user: { id: string; email: string; avatar?: string } | null
  isAuthenticated: boolean
  loading: boolean
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  // For demo purposes, provide a mock authenticated user
  const authValue: AuthContextType = {
    user: { id: 'demo-user', email: '<EMAIL>', avatar: '' },
    isAuthenticated: true,
    loading: false,
    logout: async () => {
      console.log('Mock logout called')
    }
  }

  return (
    <AuthContext.Provider value={authValue}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}