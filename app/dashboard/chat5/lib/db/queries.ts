import 'server-only';

import { createClient } from '@supabase/supabase-js';
import type { ArtifactKind } from '../../components/artifact';
import { ChatSDKError } from '../errors';

// Initialize Supabase client for server-side operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// Type definitions for database entities
export type Chat = {
  id: string;
  createdAt: string;
  title: string;
  userId: string;
  visibility: 'public' | 'private';
};

export type DBMessage = {
  id: string;
  chatId: string;
  role: string;
  parts: any;
  attachments: any;
  createdAt: string;
};

export type Vote = {
  chatId: string;
  messageId: string;
  isUpvoted: boolean;
};

export type Document = {
  id: string;
  createdAt: string;
  title: string;
  content: string | null;
  kind: 'text' | 'code' | 'image' | 'sheet';
  userId: string;
};

export type Suggestion = {
  id: string;
  documentId: string;
  documentCreatedAt: string;
  originalText: string;
  suggestedText: string;
  description: string | null;
  isResolved: boolean;
  userId: string;
  createdAt: string;
};

export type Stream = {
  id: string;
  chatId: string;
  createdAt: string;
};

// Note: User management is handled by Supabase Auth, so these functions are not needed
// but keeping them for compatibility if needed

export async function getUser(_email: string) {
  try {
    // This would query auth.users, but typically handled by Supabase Auth
    throw new ChatSDKError(
      'bad_request:database',
      'User management handled by Supabase Auth',
    );
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get user by email',
    );
  }
}

export async function createUser(_email: string, _password: string) {
  try {
    // This would be handled by Supabase Auth signup
    throw new ChatSDKError(
      'bad_request:database',
      'User creation handled by Supabase Auth',
    );
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to create user');
  }
}

export async function createGuestUser() {
  try {
    // Guest users would be handled by Supabase Auth anonymous sign in
    throw new ChatSDKError(
      'bad_request:database',
      'Guest users handled by Supabase Auth',
    );
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to create guest user',
    );
  }
}

export async function saveChat({
  id,
  userId,
  title,
  visibility,
}: {
  id: string;
  userId: string;
  title: string;
  visibility: 'public' | 'private';
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_chats')
      .insert({
        id,
        createdAt: new Date().toISOString(),
        userId,
        title,
        visibility,
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error saving chat:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in saveChat:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save chat');
  }
}

export async function deleteChatById({ id }: { id: string }) {
  try {
    // Delete related records first (foreign key constraints)
    await supabase.from('chat5_votes').delete().eq('chatId', id);
    await supabase.from('chat5_messages').delete().eq('chatId', id);
    await supabase.from('chat5_streams').delete().eq('chatId', id);

    // Delete the chat
    const { data, error } = await supabase
      .from('chat5_chats')
      .delete()
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Supabase error deleting chat:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in deleteChatById:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete chat by id',
    );
  }
}

export async function getChatsByUserId({
  id,
  limit,
  startingAfter,
  endingBefore,
}: {
  id: string;
  limit: number;
  startingAfter: string | null;
  endingBefore: string | null;
}) {
  try {
    const extendedLimit = limit + 1;
    let query = supabase
      .from('chat5_chats')
      .select('*')
      .eq('userId', id)
      .order('createdAt', { ascending: false })
      .limit(extendedLimit);

    if (startingAfter) {
      // Get the reference chat to compare timestamps
      const { data: selectedChat, error: selectedChatError } = await supabase
        .from('chat5_chats')
        .select('createdAt')
        .eq('id', startingAfter)
        .single();

      if (selectedChatError || !selectedChat) {
        throw new ChatSDKError(
          'not_found:database',
          `Chat with id ${startingAfter} not found`,
        );
      }

      query = query.gt('createdAt', selectedChat.createdAt);
    } else if (endingBefore) {
      // Get the reference chat to compare timestamps
      const { data: selectedChat, error: selectedChatError } = await supabase
        .from('chat5_chats')
        .select('createdAt')
        .eq('id', endingBefore)
        .single();

      if (selectedChatError || !selectedChat) {
        throw new ChatSDKError(
          'not_found:database',
          `Chat with id ${endingBefore} not found`,
        );
      }

      query = query.lt('createdAt', selectedChat.createdAt);
    }

    const { data: filteredChats, error } = await query;

    if (error) {
      console.error('Supabase error getting chats by user id:', error);
      throw error;
    }

    const hasMore = (filteredChats?.length || 0) > limit;

    return {
      chats: hasMore ? filteredChats!.slice(0, limit) : (filteredChats || []),
      hasMore,
    };
  } catch (error) {
    console.error('Error in getChatsByUserId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get chats by user id',
    );
  }
}

export async function getChatById({ id }: { id: string }) {
  try {
    const { data: selectedChat, error } = await supabase
      .from('chat5_chats')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Supabase error getting chat by id:', error);
      throw error;
    }

    return selectedChat;
  } catch (error) {
    console.error('Error in getChatById:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');
  }
}

export async function saveMessages({
  messages,
}: {
  messages: Array<DBMessage>;
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_messages')
      .insert(messages)
      .select();

    if (error) {
      console.error('Supabase error saving messages:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in saveMessages:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save messages');
  }
}

export async function getMessagesByChatId({ id }: { id: string }) {
  try {
    const { data, error } = await supabase
      .from('chat5_messages')
      .select('*')
      .eq('chatId', id)
      .order('createdAt', { ascending: true });

    if (error) {
      console.error('Supabase error getting messages by chat id:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getMessagesByChatId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get messages by chat id',
    );
  }
}

export async function voteMessage({
  chatId,
  messageId,
  type,
}: {
  chatId: string;
  messageId: string;
  type: 'up' | 'down';
}) {
  try {
    // Check if vote already exists
    const { data: existingVote, error: existingVoteError } = await supabase
      .from('chat5_votes')
      .select('*')
      .eq('messageId', messageId)
      .single();

    if (existingVoteError && existingVoteError.code !== 'PGRST116') {
      // PGRST116 is "not found" error, which is expected if no vote exists
      console.error('Supabase error checking existing vote:', existingVoteError);
      throw existingVoteError;
    }

    if (existingVote) {
      // Update existing vote
      const { data, error } = await supabase
        .from('chat5_votes')
        .update({ isUpvoted: type === 'up' })
        .eq('messageId', messageId)
        .eq('chatId', chatId)
        .select();

      if (error) {
        console.error('Supabase error updating vote:', error);
        throw error;
      }

      return data;
    } else {
      // Create new vote
      const { data, error } = await supabase
        .from('chat5_votes')
        .insert({
          chatId,
          messageId,
          isUpvoted: type === 'up',
        })
        .select();

      if (error) {
        console.error('Supabase error creating vote:', error);
        throw error;
      }

      return data;
    }
  } catch (error) {
    console.error('Error in voteMessage:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to vote message');
  }
}

export async function getVotesByChatId({ id }: { id: string }) {
  try {
    const { data, error } = await supabase
      .from('chat5_votes')
      .select('*')
      .eq('chatId', id);

    if (error) {
      console.error('Supabase error getting votes by chat id:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getVotesByChatId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get votes by chat id',
    );
  }
}

export async function saveDocument({
  id,
  title,
  kind,
  content,
  userId,
}: {
  id: string;
  title: string;
  kind: ArtifactKind;
  content: string;
  userId: string;
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_documents')
      .insert({
        id,
        title,
        kind,
        content,
        userId,
        createdAt: new Date().toISOString(),
      })
      .select();

    if (error) {
      console.error('Supabase error saving document:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in saveDocument:', error);
    throw new ChatSDKError('bad_request:database', 'Failed to save document');
  }
}

export async function getDocumentsById({ id }: { id: string }) {
  try {
    const { data: documents, error } = await supabase
      .from('chat5_documents')
      .select('*')
      .eq('id', id)
      .order('createdAt', { ascending: true });

    if (error) {
      console.error('Supabase error getting documents by id:', error);
      throw error;
    }

    return documents || [];
  } catch (error) {
    console.error('Error in getDocumentsById:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get documents by id',
    );
  }
}

export async function getDocumentById({ id }: { id: string }) {
  try {
    const { data: selectedDocument, error } = await supabase
      .from('chat5_documents')
      .select('*')
      .eq('id', id)
      .order('createdAt', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error('Supabase error getting document by id:', error);
      throw error;
    }

    return selectedDocument;
  } catch (error) {
    console.error('Error in getDocumentById:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get document by id',
    );
  }
}

export async function deleteDocumentsByIdAfterTimestamp({
  id,
  timestamp,
}: {
  id: string;
  timestamp: Date;
}) {
  try {
    // Delete related suggestions first
    await supabase
      .from('chat5_suggestions')
      .delete()
      .eq('documentId', id)
      .gt('documentCreatedAt', timestamp.toISOString());

    // Delete documents
    const { data, error } = await supabase
      .from('chat5_documents')
      .delete()
      .eq('id', id)
      .gt('createdAt', timestamp.toISOString())
      .select();

    if (error) {
      console.error('Supabase error deleting documents by id after timestamp:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in deleteDocumentsByIdAfterTimestamp:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete documents by id after timestamp',
    );
  }
}

export async function saveSuggestions({
  suggestions,
}: {
  suggestions: Array<Suggestion>;
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_suggestions')
      .insert(suggestions)
      .select();

    if (error) {
      console.error('Supabase error saving suggestions:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in saveSuggestions:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to save suggestions',
    );
  }
}

export async function getSuggestionsByDocumentId({
  documentId,
}: {
  documentId: string;
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_suggestions')
      .select('*')
      .eq('documentId', documentId);

    if (error) {
      console.error('Supabase error getting suggestions by document id:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getSuggestionsByDocumentId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get suggestions by document id',
    );
  }
}

export async function getMessageById({ id }: { id: string }) {
  try {
    const { data, error } = await supabase
      .from('chat5_messages')
      .select('*')
      .eq('id', id);

    if (error) {
      console.error('Supabase error getting message by id:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getMessageById:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message by id',
    );
  }
}

export async function deleteMessagesByChatIdAfterTimestamp({
  chatId,
  timestamp,
}: {
  chatId: string;
  timestamp: Date;
}) {
  try {
    // Get messages to delete
    const { data: messagesToDelete, error: selectError } = await supabase
      .from('chat5_messages')
      .select('id')
      .eq('chatId', chatId)
      .gte('createdAt', timestamp.toISOString());

    if (selectError) {
      console.error('Supabase error selecting messages to delete:', selectError);
      throw selectError;
    }

    const messageIds = messagesToDelete?.map((message: any) => message.id) || [];

    if (messageIds.length > 0) {
      // Delete related votes first
      await supabase
        .from('chat5_votes')
        .delete()
        .eq('chatId', chatId)
        .in('messageId', messageIds);

      // Delete messages
      const { data, error } = await supabase
        .from('chat5_messages')
        .delete()
        .eq('chatId', chatId)
        .in('id', messageIds)
        .select();

      if (error) {
        console.error('Supabase error deleting messages:', error);
        throw error;
      }

      return data;
    }
  } catch (error) {
    console.error('Error in deleteMessagesByChatIdAfterTimestamp:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete messages by chat id after timestamp',
    );
  }
}

export async function updateChatVisiblityById({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: 'private' | 'public';
}) {
  try {
    const { data, error } = await supabase
      .from('chat5_chats')
      .update({ visibility })
      .eq('id', chatId)
      .select();

    if (error) {
      console.error('Supabase error updating chat visibility by id:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in updateChatVisiblityById:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to update chat visibility by id',
    );
  }
}

export async function getMessageCountByUserId({
  id,
  differenceInHours,
}: { id: string; differenceInHours: number }) {
  try {
    const twentyFourHoursAgo = new Date(
      Date.now() - differenceInHours * 60 * 60 * 1000,
    );

    // Use a more complex query with joins using RPC or a view
    // For now, let's use a simpler approach by getting messages and filtering
    const { data: messages, error } = await supabase
      .from('chat5_messages')
      .select(`
        id,
        chatId,
        role,
        createdAt,
        chat5_chats!inner(userId)
      `)
      .eq('chat5_chats.userId', id)
      .eq('role', 'user')
      .gte('createdAt', twentyFourHoursAgo.toISOString());

    if (error) {
      console.error('Supabase error getting message count by user id:', error);
      throw error;
    }

    return messages?.length || 0;
  } catch (error) {
    console.error('Error in getMessageCountByUserId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get message count by user id',
    );
  }
}

export async function createStreamId({
  streamId,
  chatId,
}: {
  streamId: string;
  chatId: string;
}) {
  try {
    const { error } = await supabase
      .from('chat5_streams')
      .insert({
        id: streamId,
        chatId,
        createdAt: new Date().toISOString()
      });

    if (error) {
      console.error('Supabase error saving stream id:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in saveStreamId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to create stream id',
    );
  }
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  try {
    const { data: streamIds, error } = await supabase
      .from('chat5_streams')
      .select('id')
      .eq('chatId', chatId)
      .order('createdAt', { ascending: true });

    if (error) {
      console.error('Supabase error getting stream ids by chat id:', error);
      throw error;
    }

    return streamIds?.map(({ id }: any) => id) || [];
  } catch (error) {
    console.error('Error in getStreamIdsByChatId:', error);
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get stream ids by chat id',
    );
  }
}
