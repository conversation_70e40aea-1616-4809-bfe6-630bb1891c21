import type { NextRequest } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { getChatsByUserId } from '../../../lib/db/queries';
import { ChatSDKError } from '../../../lib/errors';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;

    const limit = Number.parseInt(searchParams.get('limit') || '10');
    const startingAfter = searchParams.get('starting_after');
    const endingBefore = searchParams.get('ending_before');

    if (startingAfter && endingBefore) {
      return new ChatSDKError(
        'bad_request:api',
        'Only one of starting_after or ending_before can be provided.',
      ).toResponse();
    }

    // Get user ID from session instead of header
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    console.log('API: Session check result:', {
      hasSession: !!session,
      userId: session?.user?.id,
      sessionError
    });

    let userId: string | null = null;

    if (session?.user?.id) {
      userId = session.user.id;
    } else {
      // Fallback: try to get from header (for compatibility)
      userId = request.headers.get("x-user-id");
      console.log('API: Fallback to header userId:', userId);

      // For demo/development: use demo user if no session or header
      if (!userId && process.env.NODE_ENV === 'development') {
        userId = 'demo-user';
        console.log('API: Using demo user for development');
      }
    }

    if (!userId) {
      console.log('API: No user ID found, returning unauthorized');
      return new ChatSDKError('unauthorized:chat').toResponse();
    }

    console.log('API: Getting chats for user:', userId);

    // First, let's test if we can connect to the database at all
    try {
      const { data: testData, error: testError } = await supabase
        .from('chat5_chats')
        .select('id')
        .limit(1);

      console.log('API: Database test result:', { testData, testError });

      if (testError) {
        console.error('API: Database connection test failed:', testError);
        return Response.json({
          error: 'Database connection failed',
          details: testError.message,
          code: testError.code,
          hint: testError.hint
        }, { status: 500 });
      }
    } catch (dbTestError) {
      console.error('API: Database test exception:', dbTestError);
      return Response.json({
        error: 'Database test exception',
        details: dbTestError instanceof Error ? dbTestError.message : 'Unknown error'
      }, { status: 500 });
    }

    const chats = await getChatsByUserId({
      id: userId,
      limit,
      startingAfter,
      endingBefore,
    });

    console.log('API: Successfully retrieved chats:', chats.chats?.length || 0);
    return Response.json(chats);
  } catch (error) {
    console.error('API: Error in GET /api/history:', error);
    return new ChatSDKError('bad_request:api', 'Internal server error').toResponse();
  }
}
